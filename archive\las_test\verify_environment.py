#!/usr/bin/env python3
"""
Environment verification script for TFT Well project.
Checks if current environment matches the requirements.
"""

import sys
import importlib.util

def check_package_version(package_name, expected_version, import_name=None):
    """Check if a package is installed with the expected version."""
    if import_name is None:
        import_name = package_name
    
    try:
        spec = importlib.util.find_spec(import_name)
        if spec is not None:
            module = importlib.import_module(import_name)
            actual_version = getattr(module, '__version__', 'Unknown')
            
            if actual_version == expected_version:
                print(f"✅ {package_name}: {actual_version} (matches)")
                return True
            else:
                print(f"⚠️  {package_name}: {actual_version} (expected {expected_version})")
                return True  # Still installed, just different version
        else:
            print(f"❌ {package_name}: Not installed")
            return False
    except ImportError:
        print(f"❌ {package_name}: Import failed")
        return False

def main():
    """Main verification function."""
    print("🔍 TFT Well Environment Verification")
    print("=" * 50)
    
    # Check Python version
    python_version = sys.version_info
    print(f"Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    print("\n📦 Checking core dependencies...")
    
    # Core dependencies with expected versions from your environment
    dependencies = [
        ("numpy", "1.24.4", "numpy"),
        ("pandas", "2.2.2", "pandas"),
        ("scikit-learn", "1.6.1", "sklearn"),
        ("tensorflow", "2.16.2", "tensorflow"),
        ("matplotlib", "3.10.1", "matplotlib"),
        ("seaborn", "0.13.2", "seaborn"),
        ("wget", "3.2", "wget"),
        ("pyunpack", "0.3", "pyunpack"),
    ]

    # Optional dependencies (not required for basic functionality)
    optional_dependencies = [
        ("tensorflow-probability", "0.25.0", "tensorflow_probability"),
        ("patool", "4.0.1", "patool"),
    ]
    
    all_installed = True
    
    for package_name, expected_version, import_name in dependencies:
        if not check_package_version(package_name, expected_version, import_name):
            all_installed = False

    print("\n📦 Checking optional dependencies...")
    for package_name, expected_version, import_name in optional_dependencies:
        check_package_version(package_name, expected_version, import_name)
    
    print("\n🧪 Testing TensorFlow functionality...")
    try:
        import tensorflow as tf  # type: ignore[import]
        import tensorflow.compat.v1 as tf_v1  # type: ignore[import]
        
        print(f"✅ TensorFlow {tf.__version__} imported successfully")
        print("✅ TensorFlow v1 compatibility mode available")
        
        # Test basic operation
        tf_v1.disable_v2_behavior()
        _ = tf_v1.constant([1, 2, 3])  # Test tensor creation
        print("✅ TensorFlow operations working")
        
    except Exception as e:
        print(f"❌ TensorFlow test failed: {e}")
        all_installed = False
    
    print("\n📋 Summary:")
    if all_installed:
        print("🎉 Environment verification PASSED!")
        print("✅ All required packages are installed")
        print("✅ TensorFlow is working correctly")
        print("✅ Ready to run TFT Well project")
    else:
        print("⚠️  Environment verification FAILED!")
        print("❌ Some packages are missing or incompatible")
        print("💡 Run: pip install -r requirements.txt")
        print("💡 Or: python setup_environment.py")
    
    print(f"\n📁 Current working directory: {sys.path[0]}")
    print("🚀 To run the project:")
    print("   python script_download_data.py volatility .")
    print("   python script_train_fixed_params.py volatility .")

if __name__ == "__main__":
    main()
