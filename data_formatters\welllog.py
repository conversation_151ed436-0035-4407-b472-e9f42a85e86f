# coding=utf-8
# Copyright 2025 The Google Research Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Custom formatting functions for Well Log dataset.

Defines dataset specific column definitions and data transformations for
predicting P-wave velocity from gamma ray, resistivity, density, and neutron logs.
"""

import data_formatters.base
import libs.utils as utils
import pandas as pd
import sklearn.preprocessing
import numpy as np

GenericDataFormatter = data_formatters.base.GenericDataFormatter
DataTypes = data_formatters.base.DataTypes
InputTypes = data_formatters.base.InputTypes


class WellLogFormatter(GenericDataFormatter):
  """Defines and formats data for the well log dataset.

  This formatter is designed to predict P-wave velocity using:
  - Gamma Ray (GR) as observed input
  - Resistivity (RT) as observed input  
  - Density (RHOB) as observed input
  - Neutron Porosity (NPHI) as observed input
  - Depth as time index
  - Well identifier as entity ID

  Attributes:
    column_definition: Defines input and data type of column used in the
      experiment.
    identifiers: Entity identifiers used in experiments.
  """

  @property
  def _column_definition(self):
    """Defines order, input type and data type of each column."""
    return [
        ('well_id', DataTypes.CATEGORICAL, InputTypes.ID),
        ('depth', DataTypes.REAL_VALUED, InputTypes.TIME),
        ('p_wave', DataTypes.REAL_VALUED, InputTypes.TARGET),
        ('gamma_ray', DataTypes.REAL_VALUED, InputTypes.OBSERVED_INPUT),
        ('resistivity', DataTypes.REAL_VALUED, InputTypes.OBSERVED_INPUT),
        ('density', DataTypes.REAL_VALUED, InputTypes.OBSERVED_INPUT),
        ('neutron', DataTypes.REAL_VALUED, InputTypes.OBSERVED_INPUT),
        ('depth_normalized', DataTypes.REAL_VALUED, InputTypes.KNOWN_INPUT),
    ]

  def __init__(self):
    """Initialises formatter."""
    super().__init__()

    self.identifiers = None
    self._real_scalers = None
    self._cat_scalers = None
    self._target_scaler = None
    self._num_classes_per_cat_input = None

  def split_data(self, df, valid_boundary=0.7, test_boundary=0.85):
    """Splits data frame into training-validation-test data frames.

    This also calibrates scaling object, and transforms data for each split.

    Args:
      df: Source data frame to split.
      valid_boundary: Fraction of data for training (default 0.7)
      test_boundary: Fraction of data for training+validation (default 0.85)

    Returns:
      Tuple of transformed (train, valid, test) data.
    """

    print('Formatting train-valid-test splits for well log data.')

    # Sort by well_id and depth to ensure proper temporal ordering
    df = df.sort_values(['well_id', 'depth']).reset_index(drop=True)
    
    # Split data by depth within each well
    train_list = []
    valid_list = []
    test_list = []
    
    for well_id in df['well_id'].unique():
        well_data = df[df['well_id'] == well_id].copy()
        n_samples = len(well_data)
        
        train_end = int(n_samples * valid_boundary)
        valid_end = int(n_samples * test_boundary)
        
        train_list.append(well_data.iloc[:train_end])
        valid_list.append(well_data.iloc[train_end:valid_end])
        test_list.append(well_data.iloc[valid_end:])
    
    train = pd.concat(train_list, ignore_index=True)
    valid = pd.concat(valid_list, ignore_index=True)
    test = pd.concat(test_list, ignore_index=True)

    print(f"Train samples: {len(train)}, Valid samples: {len(valid)}, Test samples: {len(test)}")

    self.set_scalers(train)

    return (self.transform_inputs(data) for data in [train, valid, test])

  def set_scalers(self, df):
    """Calibrates scalers using the data supplied.

    Args:
      df: Data to use to calibrate scalers.
    """
    print('Setting scalers with training data...')

    column_definitions = self.get_column_definition()
    id_column = utils.get_single_col_by_input_type(InputTypes.ID,
                                                   column_definitions)
    target_column = utils.get_single_col_by_input_type(InputTypes.TARGET,
                                                       column_definitions)

    # Set identifiers
    self.identifiers = list(df[id_column].unique())

    # Format real scalers
    real_inputs = utils.extract_cols_from_data_type(
        DataTypes.REAL_VALUED, column_definitions,
        {InputTypes.ID, InputTypes.TIME})

    # Initialise scaler caches
    self._real_scalers = {}
    self._target_scaler = {}
    for identifier, sliced in df.groupby(id_column):

      if len(sliced) >= self._min_samples_for_scaling:
        data = sliced[real_inputs].values
        targets = sliced[[target_column]].values

        self._real_scalers[identifier] = sklearn.preprocessing.StandardScaler().fit(data)
        self._target_scaler[identifier] = (targets.mean(), targets.std())

    # Format categorical scalers
    categorical_inputs = utils.extract_cols_from_data_type(
        DataTypes.CATEGORICAL, column_definitions,
        {InputTypes.ID})

    categorical_scalers = {}
    num_classes = []
    for col in categorical_inputs:
      # Set all to str so that we don't have mixed integer/string columns
      srs = df[col].apply(str)
      categorical_scalers[col] = sklearn.preprocessing.LabelEncoder().fit(
          srs.values)
      num_classes.append(srs.nunique())

    # Set categorical scaler outputs
    self._cat_scalers = categorical_scalers
    self._num_classes_per_cat_input = num_classes

  @property
  def _min_samples_for_scaling(self):
    """Minimum number of samples required for scaling."""
    return 50

  def transform_inputs(self, df):
    """Performs feature transformation.

    Args:
      df: Data frame to transform.

    Returns:
      Transformed data frame.

    """
    if self._real_scalers is None or self._cat_scalers is None:
      raise ValueError('Scalers have not been set!')

    # Create a copy to avoid modifying original data
    output = df.copy()

    column_definitions = self.get_column_definition()
    id_column = utils.get_single_col_by_input_type(InputTypes.ID,
                                                   column_definitions)

    real_inputs = utils.extract_cols_from_data_type(
        DataTypes.REAL_VALUED, column_definitions, {InputTypes.ID, InputTypes.TIME})
    categorical_inputs = utils.extract_cols_from_data_type(
        DataTypes.CATEGORICAL, column_definitions, {InputTypes.ID})

    # Format real inputs
    df_list = []
    for identifier, sliced in df.groupby(id_column):

      # Filter out any trajectories that are too short
      if len(sliced) >= self._min_samples_for_scaling:
        if self._real_scalers is None or identifier not in self._real_scalers:
          raise ValueError('Identifier {} is not present in training data'.format(identifier))

        sliced_copy = sliced.copy()
        sliced_copy[real_inputs] = self._real_scalers[identifier].transform(
            sliced_copy[real_inputs].values)
        df_list.append(sliced_copy)

    output = pd.concat(df_list, axis=0)

    # Format categorical inputs
    for col in categorical_inputs:
      if self._cat_scalers is None or col not in self._cat_scalers:
        raise ValueError('Categorical scaler for column {} is not present in training data'.format(col))
      string_df = df[col].apply(str)
      output[col] = self._cat_scalers[col].transform(string_df)

    return output

  def format_predictions(self, df):
    """Reverts any normalisation to give predictions in original scale.

    Args:
      df: Dataframe of model predictions.

    Returns:
      Data frame of unnormalised predictions.
    """
    if self._target_scaler is None:
      raise ValueError('Target scaler has not been set!')

    output = df.copy()
    column_names = df.columns

    # We need to handle multiple identifiers for denormalization
    # For simplicity, we'll use the first identifier's scaler
    # In practice, you might want to track which identifier each prediction belongs to
    if self.identifiers:
      first_identifier = self.identifiers[0]
      if first_identifier in self._target_scaler:
        mean, std = self._target_scaler[first_identifier]
        for col in column_names:
          if col not in {'forecast_time', 'identifier'}:
            output[col] = (df[col] * std) + mean

    return output

  def get_fixed_params(self):
    """Returns fixed model parameters for experiments."""

    fixed_params = {
        'total_time_steps': 30,  # Reduced for well log data
        'num_encoder_steps': 20,  # History length
        'num_epochs': 50,
        'early_stopping_patience': 5,
        'multiprocessing_workers': 1,  # Reduced for stability
    }

    return fixed_params

  def get_default_model_params(self):
    """Returns default optimised model parameters."""

    model_params = {
        'dropout_rate': 0.1,
        'hidden_layer_size': 64,  # Reduced for well log data
        'learning_rate': 0.001,
        'minibatch_size': 32,  # Reduced batch size
        'max_gradient_norm': 1.0,
        'num_heads': 4,
        'stack_size': 1,
    }

    return model_params

  def get_num_samples_for_calibration(self):
    """Gets the default number of training and validation samples.
    
    Returns:
      Tuple of (training samples, validation samples)
    """
    return 1000, 200  # Reduced for well log data
