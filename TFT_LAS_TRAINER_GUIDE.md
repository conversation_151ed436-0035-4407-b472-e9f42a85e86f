# TFT LAS Trainer - Complete User Guide

## Overview

The `tft_las_trainer.py` script is a comprehensive, standalone tool for training and validating Temporal Fusion Transformer (TFT) models with LAS (Log ASCII Standard) well log data. It provides a complete workflow from data loading to visualization.

## Features

✅ **Interactive LAS File Selection** - Choose specific files or use all available files  
✅ **Automatic LAS Processing** - Uses lasio library for robust LAS file handling  
✅ **TFT Model Training** - Full TFT training with well log data  
✅ **Prediction Validation** - Comprehensive evaluation metrics  
✅ **Visualization & Plotting** - Multiple plots showing prediction quality  
✅ **Results Export** - Saves plots and summary reports  

## Prerequisites

Ensure you have the required dependencies installed:

```bash
pip install lasio pandas numpy matplotlib seaborn tensorflow scikit-learn
```

## Usage

### Interactive Mode (Recommended)

Run the script interactively to select specific LAS files:

```bash
python tft_las_trainer.py
```

This will:
1. Show available LAS files in the `Las data` directory
2. Allow you to select specific files (e.g., `1,3,5` or `1-3`)
3. Guide you through the complete training workflow

### Batch Mode

Process all available LAS files automatically:

```bash
python tft_las_trainer.py --batch
```

### Command Line Options

```bash
python tft_las_trainer.py [OPTIONS]

Options:
  --las-dir DIR         Directory containing LAS files (default: 'Las data')
  --output-dir DIR      Output directory for results (default: 'tft_results')
  --full-training       Use full training mode (slower but more accurate)
  --batch              Run in batch mode (use all available files)
  --help               Show help message
```

### Examples

```bash
# Interactive mode with custom directories
python tft_las_trainer.py --las-dir "my_las_files" --output-dir "my_results"

# Batch mode with full training
python tft_las_trainer.py --batch --full-training

# Quick test with default settings
python tft_las_trainer.py
```

## Workflow Steps

### 1. File Selection
- Lists all `.las` files in the specified directory
- Interactive selection interface:
  - Enter file numbers: `1,3,5` (select files 1, 3, and 5)
  - Enter ranges: `1-3` (select files 1, 2, and 3)
  - Enter `all` to select all files
  - Enter `quit` to exit

### 2. Data Loading & Processing
- Loads LAS files using the `lasio` library
- Extracts required curves:
  - **Gamma Ray** (GR, GAMMA_RAY, GAMMA, SGR)
  - **Resistivity** (RT, RES, RESISTIVITY, ILD, LLD)
  - **Density** (RHOB, DENS, DENSITY, RHOZ)
  - **Neutron** (NPHI, NEUT, NEUTRON, PHIN)
  - **P-wave** (P-WAVE, PWAVE, VP, PVEL, DT)
  - **Depth** (DEPTH, DEPT, MD, TVDSS)
- Handles unit conversions (e.g., DT slowness to velocity)
- Cleans data (removes nulls, outliers)

### 3. TFT Model Training
- Splits data into train/validation/test sets
- Configures TFT model parameters
- Trains the model to predict P-wave velocity
- Supports both TensorFlow 1.x and 2.x compatibility

### 4. Prediction & Evaluation
- Makes predictions on test data
- Calculates performance metrics:
  - **RMSE** (Root Mean Square Error)
  - **MAE** (Mean Absolute Error)
  - **R²** (Coefficient of Determination)
  - **MSE** (Mean Square Error)

### 5. Visualization
Creates comprehensive plots:
- **Prediction vs Actual** scatter plot
- **Residuals** plot
- **Distribution comparison** histogram
- **Time series** sample predictions
- **Metrics summary** bar chart

## Output Files

The script generates several output files in the specified output directory:

```
tft_results/
├── prediction_results.png      # Main prediction visualization
├── metrics_summary.png         # Performance metrics chart
├── results_summary.txt         # Detailed text summary
└── model/                      # Trained model files (if applicable)
```

## Data Requirements

### LAS File Format
- Standard LAS format (.las extension)
- Must contain the required well log curves
- Depth column for time series indexing

### Curve Requirements
The script looks for these curves (case-insensitive):
- **Gamma Ray**: GR, GAMMA_RAY, GAMMA, SGR
- **Resistivity**: RT, RES, RESISTIVITY, ILD, LLD
- **Density**: RHOB, DENS, DENSITY, RHOZ
- **Neutron**: NPHI, NEUT, NEUTRON, PHIN
- **P-wave**: P-WAVE, PWAVE, VP, PVEL, DT

## Training Modes

### Test Mode (Default)
- Faster training for quick testing
- 5 epochs, smaller model size
- Good for initial validation

### Full Training Mode
- Complete training configuration
- More epochs, full model size
- Better accuracy, longer training time
- Use `--full-training` flag

## Troubleshooting

### Common Issues

1. **"No LAS files found"**
   - Check the `--las-dir` path
   - Ensure files have `.las` extension

2. **"Failed to extract required curves"**
   - Verify LAS files contain the required curves
   - Check curve naming conventions

3. **TensorFlow compatibility issues**
   - The script handles both TF 1.x and 2.x
   - For TF 2.x, uses simplified training mode

4. **Memory issues**
   - Use test mode for large datasets
   - Reduce the number of files processed

### Performance Tips

- Start with test mode for quick validation
- Use specific file selection for focused analysis
- Monitor memory usage with large datasets
- Check data quality before training

## Integration

This script can be integrated into larger workflows:

```python
from tft_las_trainer import TFTLASTrainer

# Initialize trainer
trainer = TFTLASTrainer("my_las_dir", "my_output")

# Load specific files
files = ["well1.las", "well2.las"]
data = trainer.load_and_combine_data(files)

# Train and evaluate
model, results, test_data, predictions = trainer.train_model(data)
metrics = trainer.evaluate_predictions(test_data, predictions)
trainer.create_visualizations(test_data, predictions, metrics)
```

## Support

For issues or questions:
1. Check the console output for error messages
2. Verify LAS file format and content
3. Ensure all dependencies are installed
4. Review the generated `results_summary.txt` for details
