# TensorFlow Setup Guide

## 🚨 Important: Pylance Import Warnings

If you see TensorFlow import warnings in VS Code, **this is normal and doesn't affect functionality**. The warnings appear because:

1. <PERSON><PERSON><PERSON> can't find TensorFlow in your environment
2. Your Python interpreter path may not be correctly set
3. TensorFlow may not be installed

## ✅ Quick Fix Steps

### Step 1: Verify TensorFlow Installation

Open a terminal and run:

```bash
# Activate your environment
C:\Users\<USER>\codellm\Scripts\activate

# Check if TensorFlow is installed
python -c "import tensorflow as tf; print('TensorFlow version:', tf.__version__)"
```

### Step 2: Install TensorFlow (if missing)

```bash
# Install TensorFlow
pip install tensorflow

# Or install all requirements
pip install -r requirements.txt
```

### Step 3: Set Correct Python Interpreter in VS Code

1. Press `Ctrl+Shift+P`
2. Type "Python: Select Interpreter"
3. Choose: `C:\Users\<USER>\codellm\Scripts\python.exe`

### Step 4: Restart VS Code

Close and reopen VS Code to refresh Pylance.

## 🔧 Alternative: Run Setup Script

We've included a setup script to automate the process:

```bash
python setup_environment.py
```

This will:
- Check your Python version
- Verify all dependencies
- Install missing packages
- Test TensorFlow import

## 📋 Current Configuration

Your project is configured with:

- ✅ **TensorFlow 2.x** with v1 compatibility mode
- ✅ **Enhanced session management** for modern TF patterns
- ✅ **Robust error handling** throughout the codebase
- ✅ **Type safety** with proper None checks

## 🎯 The Bottom Line

**Your code will work perfectly** even with Pylance warnings, as long as TensorFlow is installed in your environment.

The warnings are just Pylance being cautious - they don't indicate actual code problems.

## 🚀 Running the Code

Once TensorFlow is installed, you can run:

```bash
# Hyperparameter optimization
python script_hyperparam_opt.py volatility . no yes

# Fixed parameter training
python script_train_fixed_params.py volatility .
```

## 📞 Troubleshooting

If you still see issues:

1. **Check Python path**: Ensure VS Code is using the correct interpreter
2. **Reinstall TensorFlow**: `pip uninstall tensorflow && pip install tensorflow`
3. **Clear cache**: Delete `__pycache__` folders and restart VS Code
4. **Check environment**: Make sure you're in the `codellm` environment

## 🎉 Success Indicators

You'll know everything is working when:
- ✅ `python setup_environment.py` runs without errors
- ✅ TensorFlow imports successfully in Python
- ✅ Your training scripts run without import errors
